('C:\\Users\\<USER>\\Desktop\\win\\zqcf_AI\\zqcf_league\\build\\zqcf_AI_Plus_3.11\\PYZ-00.pyz',
 [('PIL',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('cssselect',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\doctest.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('markdown',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\__init__.py',
   'PYMODULE'),
  ('markdown.__meta__',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\__meta__.py',
   'PYMODULE'),
  ('markdown.blockparser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\blockparser.py',
   'PYMODULE'),
  ('markdown.blockprocessors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\blockprocessors.py',
   'PYMODULE'),
  ('markdown.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\core.py',
   'PYMODULE'),
  ('markdown.extensions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\__init__.py',
   'PYMODULE'),
  ('markdown.extensions.abbr',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\abbr.py',
   'PYMODULE'),
  ('markdown.extensions.admonition',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\admonition.py',
   'PYMODULE'),
  ('markdown.extensions.attr_list',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\attr_list.py',
   'PYMODULE'),
  ('markdown.extensions.codehilite',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\codehilite.py',
   'PYMODULE'),
  ('markdown.extensions.def_list',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\def_list.py',
   'PYMODULE'),
  ('markdown.extensions.extra',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\extra.py',
   'PYMODULE'),
  ('markdown.extensions.fenced_code',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\fenced_code.py',
   'PYMODULE'),
  ('markdown.extensions.footnotes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\footnotes.py',
   'PYMODULE'),
  ('markdown.extensions.legacy_attrs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\legacy_attrs.py',
   'PYMODULE'),
  ('markdown.extensions.legacy_em',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\legacy_em.py',
   'PYMODULE'),
  ('markdown.extensions.md_in_html',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\md_in_html.py',
   'PYMODULE'),
  ('markdown.extensions.meta',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\meta.py',
   'PYMODULE'),
  ('markdown.extensions.nl2br',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\nl2br.py',
   'PYMODULE'),
  ('markdown.extensions.sane_lists',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\sane_lists.py',
   'PYMODULE'),
  ('markdown.extensions.smarty',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\smarty.py',
   'PYMODULE'),
  ('markdown.extensions.tables',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\tables.py',
   'PYMODULE'),
  ('markdown.extensions.toc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\toc.py',
   'PYMODULE'),
  ('markdown.extensions.wikilinks',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\extensions\\wikilinks.py',
   'PYMODULE'),
  ('markdown.htmlparser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\htmlparser.py',
   'PYMODULE'),
  ('markdown.inlinepatterns',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\inlinepatterns.py',
   'PYMODULE'),
  ('markdown.postprocessors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\postprocessors.py',
   'PYMODULE'),
  ('markdown.preprocessors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\preprocessors.py',
   'PYMODULE'),
  ('markdown.serializers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\serializers.py',
   'PYMODULE'),
  ('markdown.treeprocessors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\treeprocessors.py',
   'PYMODULE'),
  ('markdown.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\markdown\\util.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.py2_warn',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\py2_warn.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('win32com',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\users\\<USER>\\desktop\\win\\awd\\.venv\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
